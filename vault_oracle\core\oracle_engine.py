# vault_oracle/core/oracle_engine.py
# -*- coding: utf-8 -*-

#!/usr/bin/env python3
"""
Expert Oracle Engine - Quantum-Inspired Basketball Analytics Orchestrator
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

The transcendent orchestration engine of the Hyper Medusa Neural Vault - Enhanced for Expert-Level Operations.
This quantum-inspired engine coordinates the symphony of divine components with basketball-aware intelligence,
temporal coherence management, and advanced prophetic capabilities for elite basketball analytics.

Expert Features:
- Quantum-entangled component orchestration
- Basketball-aware temporal analysis with season phase detection
- Advanced prophecy generation with uncertainty quantification
- Multi-dimensional performance monitoring and optimization
- Intelligent adaptive scheduling based on basketball rhythms
- Divine intervention protocols with automatic failover
- Real-time quantum state synchronization across all components
- Professional-grade error handling with cosmic collapse prevention
- Basketball domain expertise integration throughout all operations
- Celestial harmony alignment for optimal prediction accuracy

Architecture:
- Expert-level component lifecycle management
- Quantum temporal flux stabilization with basketball rhythm synchronization
- Advanced memory management with quantum entanglement tracking
- Intelligent load balancing and resource optimization
- Basketball-specific data flow orchestration
- Divine intervention cascading with automatic recovery
- Professional monitoring and alerting systems
- Cosmic event correlation and pattern recognition
- Multi-threaded quantum processing with basketball context awareness
- Expert-level configuration validation and dynamic adjustment

Basketball Intelligence Integration:
- Season phase-aware prophecy generation (preseason → finals)
- Game day intensity modulation and priority boosting
- Player performance rhythm synchronization
- Team chemistry quantum entanglement tracking
- Basketball IQ-enhanced prediction algorithms
- Clutch time temporal acceleration protocols
- Playoff intensity multiplier activation
- Championship probability quantum modeling
"""

import sys
import os
import asyncio
import logging
import threading
import uuid
from pathlib import Path
from typing import Dict, Any, Optional, Union, List, Tuple, Set
from dataclasses import dataclass, field
from enum import Enum
from collections import deque, defaultdict
import json
import time
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
import base64
from cryptography.fernet import Fernet # Used for generating Fernet-compatible keys
from vault_oracle.core.oracle_constants import (
    QUANTUM_STATES,
    BASKETBALL_CONTEXT,
    SEASON_PHASES,
    ICHOR_VITALITY,
    DIVINE_INTERVENTION,
    get_quantum_state_modifier,
    get_basketball_context_modifier,
)
from vault_oracle.core.oracle_focus import oracle_focus
from pydantic import BaseModel, Field, SecretStr, AnyUrl, ValidationError
from pydantic_settings import BaseSettings, SettingsConfigDict
try:
    from vault_oracle.core.vault_config import VaultConfig
except ImportError:
    VaultConfig = None

try:
    from vault_oracle.core.vault_loader import VaultLoader
except ImportError:
    VaultLoader = None

try:
    from vault_oracle.core.OracleMemory import OracleMemory
except ImportError:
    OracleMemory = None

try:
    from vault_oracle.core.quantum_security_enhancements import QuantumSecurityEnhancements
except ImportError:
    QuantumSecurityEnhancements = None

try:
    from vault_oracle.core.temporal_flux import TemporalFluxStabilizer
except ImportError:
    TemporalFluxStabilizer = None

try:
    from vault_oracle.interfaces.divine_messenger import DivineMessenger
except ImportError:
    DivineMessenger = None

try:
    from vault_oracle.interfaces.expert_messaging_orchestrator import ExpertMessagingOrchestrator
except ImportError:
    ExpertMessagingOrchestrator = None

try:
    from vault_oracle.core.quantum_forge import QuantumForge
except ImportError:
    QuantumForge = None

try:
    from vault_oracle.utils.AmbrosiaHasher import AmbrosiaHasher
except ImportError:
    AmbrosiaHasher = None
try:
    from vault_oracle.essence.ichor_flow import IchorFlow
except ImportError:
    IchorFlow = None

try:
    from vault_oracle.core.oracle_exceptions import CosmicCollapse
except ImportError:
    CosmicCollapse = Exception

try:
    from vault_oracle.core.vault_config import ConfigValidator
except ImportError:
    ConfigValidator = None

try:
    from src.core.league_season_manager import get_current_league_context
except ImportError:
    def get_current_league_context():
        return "NBA"

try:
    from vault_oracle.core.QuantumEntangler import QuantumEntangler
except ImportError:
    QuantumEntangler = None

try:
    from vault_oracle.core.quantum_forge import HydraGenome
except ImportError:
    HydraGenome = None

try:
    from vault_oracle.core.oracle_exceptions import (
        ModelIntegrityError,
        QuantumEntanglementFailure,
        StateValidationError
    )
except ImportError:
    # Fallback exception classes
    ModelIntegrityError = Exception
    QuantumEntanglementFailure = Exception
    StateValidationError = Exception


from datetime import (
    datetime,
    timedelta,
    timezone,
) # Import timezone for consistent datetime

# Add project root to Python path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# --- Configure Logger for the Oracle Engine (Global Scope - as early as possible) ---
logger = logging.getLogger("OracleEngine")
# Ensure basicConfig is only run if no handlers are set up yet
if not logger.handlers:
    logging.basicConfig(
        level=logging.INFO,
        format="𓄿 %(asctime)s 𓃬 %(levelname)s 𓄢 %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
        handlers=[logging.StreamHandler(sys.stdout)], # Direct output to stdout
    )
    logger = logging.getLogger("OracleEngine") # Re-get logger after basicConfig
    logger.info(" MEDUSA VAULT: Oracle Engine logger initialized.")
# --- End Logger Configuration ---

# --- Set dummy environment variables for testing purposes (MUST be at the top) ---
# These must be set BEFORE any modules relying on them (like Pydantic BaseSettings) are imported.

# These should match the keys expected by ConfigValidator in vault_config.py
# and the environment prefix (HYPER_MEDUSA_)
os.environ["HYPER_MEDUSA_FIREBASE_KEY"] = (
    "mock_firebase_credentials_json_string_for_testing"
)
os.environ["HYPER_MEDUSA_VAULT_ENCRYPTION_KEY"] = Fernet.generate_key().decode("utf-8")
# Add other necessary dummy env vars here if your ConfigValidator expects them
# For example:
os.environ["HYPER_MEDUSA_MEDUSA_ODDS_KEY"] = base64.urlsafe_b64encode(
    os.urandom(32)
).decode("utf-8")
os.environ["HYPER_MEDUSA_BALLDONTLIE_KEY"] = base64.urlsafe_b64encode(
    os.urandom(36)
).decode("utf-8")
os.environ["HYPER_MEDUSA_AEGIS_KEY"] = base64.urlsafe_b64encode(os.urandom(64)).decode(
    "utf-8"
)
# Adjusted to 32 bytes to ensure 44-character base64 output, as required by Fernet
os.environ["HYPER_MEDUSA_AMBROSIA_KEY"] = Fernet.generate_key().decode("utf-8")
os.environ["HYPER_MEDUSA_ATLANTIS_KEY"] = Fernet.generate_key().decode("utf-8")
os.environ["HYPER_MEDUSA_ELYSIUM_KEY"] = Fernet.generate_key().decode("utf-8")
os.environ["HYPER_MEDUSA_MNEMOSYNE_KEY"] = Fernet.generate_key().decode("utf-8")
os.environ["HYPER_MEDUSA_CONFIG_WEBHOOK"] = "https://example.com/webhook"
os.environ["HYPER_MEDUSA_SENTRY_DSN"] = "http://<EMAIL>/1"
# Adjusted to 32 bytes to ensure 44-character base64 output, as required by Fernet
os.environ["HYPER_MEDUSA_PROPHECY_SIGNING_KEY"] = Fernet.generate_key().decode("utf-8")
os.environ["HYPER_MEDUSA_BUILD_NUMBER"] = "2.1.5-beta"
os.environ["HYPER_MEDUSA_ENVIRONMENT"] = (
    "production" # Match the config file you are loading
)

logger.info(" MEDUSA VAULT: Dummy environment variables set for Oracle Engine test.")
# --- End dummy environment variables setup ---

# --- Expert-level imports for quantum-inspired basketball analytics ---
try:
    from vault_oracle.core.oracle_constants import (
        QUANTUM_STATES, BASKETBALL_CONTEXT, SEASON_PHASES, ICHOR_VITALITY,
        DIVINE_INTERVENTION, get_quantum_state_modifier, get_basketball_context_modifier
    )
    logger.info(" MEDUSA VAULT: Expert oracle constants imported successfully")
except ImportError as e:
    logger.warning(f" Could not import oracle constants: {e}. Using fallback values.")
    # Fallback constants
    QUANTUM_STATES = {"COHERENT": 0.85, "DECOHERENT": 0.35}
    BASKETBALL_CONTEXT = {"PLAYOFF_INTENSITY": 0.12, "HOME_COURT_ADVANTAGE": 0.06}
    SEASON_PHASES = {"REGULAR_SEASON": {"weight": 1.0}, "PLAYOFFS": {"weight": 1.5}}
    ICHOR_VITALITY = {"OPTIMAL_ZONE": (0.75, 0.90), "CRITICAL_ZONE": (0.20, 0.39)}
    DIVINE_INTERVENTION = {"CATASTROPHIC_FAILURE": 0.05}

    def get_quantum_state_modifier(coherence): return 1.0 + (coherence - 0.5)
    def get_basketball_context_modifier(context): return BASKETBALL_CONTEXT.get(context, 0.0)

# ValidationError already imported from pydantic above


# --- Path Manipulation for Module Imports (ensure project root is in sys.path early) ---
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.abspath(os.path.join(current_dir, "..", ".."))
if project_root not in sys.path:
    sys.path.insert(0, project_root)
# --- End Path Manipulation ---


# --- Mock/Placeholder Dependencies for Oracle Engine ---
# This section defines mock classes and functions for modules that the Oracle Engine
# depends on, allowing the Oracle Engine to be tested or run even if those modules
# are not fully implemented or available.

# 1. oracle_focus decorator
try:
    from vault_oracle.core.oracle_focus import oracle_focus
    logger.info(" MEDUSA VAULT: Successfully imported real oracle_focus.")
except ImportError as e:
    logger.warning(f" Could not import oracle_focus: {e}. Using mock decorator.")

    def oracle_focus(func):
        """Mock decorator for oracle_focus."""
        def wrapper(*args, **kwargs):
            return func(*args, **kwargs)
        return wrapper


# 2. VaultConfig and ConfigValidator (Pydantic models)
try:
    from vault_oracle.core.vault_config import (
        VaultConfig,
        ConfigValidator,
        SystemConfig,
        NeuralIchorConfig,
        RealmSecurityError,
        InvalidFernetKeyError,
    )

    IchorConfig = NeuralIchorConfig
    logger.info(
        " Successfully imported REAL Pydantic and VaultConfig models, aliasing NeuralIchorConfig as IchorConfig."
    )
except ImportError as e:
    logger.critical(
        f" Could NOT import REAL Pydantic or VaultConfig models: {e}. Production build requires all real config classes."
    )
    raise

# 3. VaultLoader (for loading config)
try:
    from vault_oracle.core.vault_loader import VaultLoader
    logger.info(" MEDUSA VAULT: Successfully imported VaultLoader.")
except ImportError as e:
    logger.critical(
        f" Could NOT import VaultLoader: {e}. Production build requires VaultLoader."
    )
    raise

# 4. OracleMemory (for temporal storage)
try:
    from vault_oracle.core.OracleMemory import (
        OracleMemory,
        QuantumMemoryError,
        MemoryIntegrityError,
        DecryptionError,
        MemoryCorruptionError,
        ExpertOracleMemory, # Assuming this is available if needed for minimal components
    )

    logger.info(" MEDUSA VAULT: Successfully imported OracleMemory.")
except ImportError as e:
    logger.critical(
        f" TITAN PROCESSING FAILED: import OracleMemory: {e}. Production build requires OracleMemory."
    )
    raise

# 9. QuantumSecurityEnhancements (for generic security features, used by OracleMemory)
try:
    from vault_oracle.core.quantum_security_enhancements import (
        QuantumSecurityEnhancements,
    )

    RealQuantumSecurityEnhancements = QuantumSecurityEnhancements
    logger.info(
        " Successfully imported QuantumSecurityEnhancements and aliased as RealQuantumSecurityEnhancements for OracleEngine's use."
    )
except ImportError as e:
    logger.critical(
        f" TITAN PROCESSING FAILED: import QuantumSecurityEnhancements from vault_oracle.core.quantum_security_enhancements: {e}. Production build requires QuantumSecurityEnhancements."
    )
    raise

# 10. TemporalFluxStabilizer
try:
    from vault_oracle.core.temporal_flux import TemporalFluxStabilizer
    logger.info(" MEDUSA VAULT: Successfully imported TemporalFluxStabilizer.")
except ImportError as e:
    logger.critical(
        f" TITAN PROCESSING FAILED: import TemporalFluxStabilizer: {e}. Production build requires TemporalFluxStabilizer."
    )
    raise

# 11. QuantumMessenger and QuantumForge (for messaging and prophecy generation)
try:
    from vault_oracle.interfaces.divine_messenger import (
        QuantumMessenger,
        QuantumMessengerConfig,
    )
    # Expert Messaging System Integration
    try:
        EXPERT_MESSAGING_AVAILABLE = True
        logger.info(" MEDUSA VAULT: Expert Messaging Orchestrator available for Oracle Engine")
    except ImportError as e:
        logger.warning(f"Expert Messaging Orchestrator not available: {e}")
        EXPERT_MESSAGING_AVAILABLE = False

    logger.info(
        " Successfully imported all production dependencies for Oracle Engine."
    )
except ImportError as e:
    logger.critical(
        f" TITAN PROCESSING FAILED: import a required production dependency: {e}. Production build requires all real classes."
    )
    raise

# Oracle Exceptions (core dependency)

# --- End Mock/Placeholder Dependencies ---


# --- Expert Oracle Engine Enums ---

class OracleState(Enum):
    """Oracle Engine operational states"""
    INITIALIZING = "initializing"
    ACTIVE = "active"
    PROPHECY_MODE = "prophecy_mode"
    BASKETBALL_ANALYSIS = "basketball_analysis"
    QUANTUM_ENTANGLED = "quantum_entangled"
    TEMPORAL_FLUX = "temporal_flux"
    DIVINE_INTERVENTION = "divine_intervention"
    COSMIC_COLLAPSE = "cosmic_collapse"
    MAINTENANCE = "maintenance"
    SHUTDOWN = "shutdown"

class BasketballSeason(Enum):
    """Basketball season phases for temporal analysis"""
    PRESEASON = "preseason"
    REGULAR_SEASON = "regular_season"
    PLAYOFFS = "playoffs"
    CONFERENCE_FINALS = "conference_finals"
    HOOPS_PANTHEON_FINALS = "finals"
    OFFSEASON = "offseason"
    DRAFT = "draft"
    FREE_AGENCY = "free_agency"

class QuantumIntensity(Enum):
    """Quantum processing intensity levels"""
    MINIMAL = 1
    LOW = 2
    MODERATE = 3
    HIGH = 4
    MAXIMUM = 5
    CHAMPIONSHIP = 6

class ProphecyType(Enum):
    """Types of basketball prophecies generated"""
    GAME_PREDICTION = "game_prediction"
    PLAYER_PERFORMANCE = "player_performance"
    TEAM_CHEMISTRY = "team_chemistry"
    CLUTCH_ANALYSIS = "clutch_analysis"
    SEASON_OUTLOOK = "season_outlook"
    PLAYOFF_BRACKET = "playoff_bracket"
    CHAMPIONSHIP_PROBABILITY = "championship_probability"

# --- End Expert Oracle Engine Enums ---


class ExpertOracleEngine:
    """
    Expert Oracle Engine - Quantum-Inspired Basketball Analytics Orchestrator
    ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

    The transcendent orchestration engine of the Hyper Medusa Neural Vault - Enhanced for Expert-Level Operations.
    This quantum-inspired engine coordinates the symphony of divine components with basketball-aware intelligence,
    temporal coherence management, and advanced prophetic capabilities for elite basketball analytics.

    Expert Features:
    - Quantum-entangled component orchestration with basketball rhythm synchronization
    - Advanced season phase detection and game state awareness
    - Multi-dimensional performance monitoring with basketball IQ integration
    - Intelligent adaptive scheduling based on basketball rhythms and temporal flux
    - Divine intervention protocols with basketball context preservation
    - Real-time quantum state synchronization across all basketball analytics components
    - Professional-grade error handling with cosmic collapse prevention
    - Basketball domain expertise integration throughout all operations
    - Celestial harmony alignment for optimal prediction accuracy with clutch-time awareness
    - Advanced analytics pipeline with playoff intensity multipliers

    Basketball Intelligence:
    - Season phase detection: preseason → regular season → playoffs → finals
    - Game day intensity modulation and priority boosting
    - Player performance rhythm synchronization and momentum tracking
    - Team chemistry quantum entanglement tracking
    - Basketball IQ-enhanced prediction algorithms with situational awareness - Clutch time temporal acceleration protocols
    - Playoff intensity multiplier activation with championship probability modeling
    - Advanced player prop and team performance correlation analysis
    """

    def __init__(self, config: Union["VaultConfig", Dict[str, Any]], env_settings: Optional["ConfigValidator"] = None):
        """
        Initializes the Expert Oracle Engine with quantum-basketball integration.

        Args:
            config: The loaded VaultConfig Pydantic model or a dictionary of configuration options.
            env_settings: Optional loaded ConfigValidator (environment settings).
        """
        # Handle both VaultConfig objects and simple dictionaries
        if isinstance(config, dict):
            try:
                # Attempt to create a VaultConfig from the provided dictionary
                # This assumes VaultConfig has defaults for its nested BaseModel fields,
                # or that the dictionary provides all necessary data.
                if VaultConfig is None:
                    logger.error(" VaultConfig not available, cannot create from dictionary")
                    raise ImportError("VaultConfig not available")
                self.config = VaultConfig(**config)
                logger.info(" MEDUSA VAULT: Initialized VaultConfig from provided dictionary.")
            except (ValidationError, ImportError) as e:
                logger.error(f" TITAN PROCESSING FAILED: VaultConfig validation error from dict: {e}. "
                             "Attempting to construct minimal fallback VaultConfig.")
                # Fallback to a minimal, hardcoded config for testing if validation fails
                # In production, this would likely raise a critical exception.
                if VaultConfig is not None:
                    self.config = VaultConfig.model_construct(
                        enable_prophecy=config.get('enable_prophecy', True),
                        enable_quantum_entanglement=config.get('enable_quantum_entanglement', True),
                        enable_basketball_analytics=config.get('enable_basketball_analytics', True),
                        basketball_season_phase=config.get('basketball_season_phase', 'regular_season'),
                        # Use default constructed sub-models for nested configs if not in dict
                        chronos_patience=VaultConfig.model_construct().chronos_patience,
                        hermes_conduits=VaultConfig.model_construct().hermes_conduits,
                        neural_settings=VaultConfig.model_construct().neural_settings,
                        hephaestus_security=VaultConfig.model_construct().hephaestus_security,
                        aegis_defense=VaultConfig.model_construct().aegis_defense,
                        neural_ichor=VaultConfig.model_construct().neural_ichor,
                        live_config=VaultConfig.model_construct().live_config,
                        mnemosyne_core=VaultConfig.model_construct().mnemosyne_core,
                        celestial_monitoring=VaultConfig.model_construct().celestial_monitoring,
                        vault_paths=VaultConfig.model_construct().vault_paths,
                        system_config=VaultConfig.model_construct().system_config,
                    )
                    logger.warning(" TITAN WARNING: Using minimal fallback VaultConfig due to dictionary parsing error.")
                else:
                    # Create a minimal config object if VaultConfig is not available
                    self.config = type('MinimalConfig', (), {
                        'enable_prophecy': config.get('enable_prophecy', True),
                        'enable_quantum_entanglement': config.get('enable_quantum_entanglement', True),
                        'enable_basketball_analytics': config.get('enable_basketball_analytics', True),
                        'basketball_season_phase': config.get('basketball_season_phase', 'regular_season')
                    })()
                    logger.warning(" TITAN WARNING: Using minimal object config due to VaultConfig unavailability.")
        else:
            self.config = config

        # Handle env_settings - if None, create a minimal one for testing
        if env_settings is None:
            try:
                if ConfigValidator is not None:
                    self.env_settings = ConfigValidator()
                    logger.info(" MEDUSA VAULT: 🔧 Created ConfigValidator for testing environment")
                else:
                    logger.warning(" ConfigValidator not available, using None.")
                    self.env_settings = None
            except Exception as e:
                logger.warning(f" Could not create ConfigValidator: {e}. Using None.")
                self.env_settings = None
        else:
            self.env_settings = env_settings

        # Now that self.config and self.env_settings are guaranteed to exist,
        # safely initialize other attributes
        self._is_initialized = False
        self._quantum_coherence = 0.0
        self._basketball_context = {}
        # Ensure basketball_season_phase is accessed safely from config
        self._current_season_phase = getattr(self.config, 'basketball_season_phase', 'regular_season')
        self._temporal_flux_history = deque(maxlen=100)
        self._prophecy_accuracy_tracker = defaultdict(list)

        # Initialize state and basketball season
        self.state = OracleState.INITIALIZING
        self.basketball_season = BasketballSeason.REGULAR_SEASON

        # Set basketball season based on config
        season_mapping = {
            'preseason': BasketballSeason.PRESEASON,
            'regular': BasketballSeason.REGULAR_SEASON,
            'regular_season': BasketballSeason.REGULAR_SEASON,
            'playoffs': BasketballSeason.PLAYOFFS,
            'finals': BasketballSeason.HOOPS_PANTHEON_FINALS,
            'offseason': BasketballSeason.OFFSEASON
        }
        if self._current_season_phase in season_mapping:
            self.basketball_season = season_mapping[self._current_season_phase]

        self._performance_metrics = {
            "prophecies_generated": 0,
            "accuracy_rate": 0.0,
            "quantum_coherence_avg": 0.0,
            "basketball_context_hits": 0,
            "divine_interventions": 0
        }

        logger.info(" MEDUSA VAULT: Initiating Expert Oracle Engine with Basketball Intelligence...")

        # Initialize quantum state with basketball awareness
        self._initialize_quantum_state()

        # Initialize core components with expert-level enhancements (simplified for testing)
        try:
            self._initialize_expert_components()
        except CosmicCollapse as e: # Catch specific CosmicCollapse for critical failures
            logger.critical(f" TITAN PROCESSING FAILED: Core component initialization failed: {e}. Entering simplified mode.")
            self._initialize_minimal_components() # Fallback
        except Exception as e: # Catch any other unexpected errors during expert component init
            logger.critical(f" TITAN PROCESSING FAILED: Unexpected error during expert component initialization: {e}. Entering simplified mode.", exc_info=True)
            self._initialize_minimal_components() # Fallback

        # Setup basketball intelligence system
        self._initialize_basketball_intelligence()

        # Set state to active
        self.state = OracleState.ACTIVE
        self._initialize_monitoring_systems()

        self._is_initialized = True
        logger.info(" MEDUSA VAULT: Expert Oracle Engine initialization complete with Basketball Intelligence.")

    @oracle_focus
    def _initialize_quantum_state(self):
        """Initialize quantum state with basketball-aware parameters."""
        try:
            # Set initial quantum coherence based on current basketball context
            current_time = datetime.now()
            is_basketball_season = self._is_basketball_season(current_time)

            try:

                # Get comprehensive multi-league context
                multi_context = get_current_league_context()

                # Check if either league is active
                any_league_active = multi_context.nba_context.is_active or multi_context.wnba_context.is_active

                if any_league_active:
                    base_coherence = QUANTUM_STATES["COHERENT"]

                    # Use primary league's quantum coherence
                    if multi_context.primary_league.value == "NBA":
                        league_coherence = multi_context.nba_context.quantum_coherence
                        season_phase = f"NBA_{multi_context.nba_context.season_phase.value}"
                    else:
                        league_coherence = multi_context.wnba_context.quantum_coherence
                        season_phase = f"WNBA_{multi_context.wnba_context.season_phase.value}"

                    self._quantum_coherence = base_coherence * league_coherence
                    self._current_season_phase = season_phase

                    logger.info(f"🏀 Multi-League Season Active - NBA: {multi_context.nba_context.season_phase.value} "
                              f"(Active: {multi_context.nba_context.is_active}), "
                              f"WNBA: {multi_context.wnba_context.season_phase.value} "
                              f"(Active: {multi_context.wnba_context.is_active}), "
                              f"Primary: {multi_context.primary_league.value}, "
                              f"Coherence: {self._quantum_coherence:.3f}")
                else:
                    self._quantum_coherence = QUANTUM_STATES["DECOHERENT"]
                    self._current_season_phase = "BOTH_LEAGUES_OFFSEASON"
                    logger.info(f"⏹️ Both NBA and WNBA in offseason - Coherence: {self._quantum_coherence:.3f}")

            except ImportError:
                # Fallback to old NBA-only logic
                if is_basketball_season:
                    base_coherence = QUANTUM_STATES["COHERENT"]
                    season_phase = self._detect_season_phase(current_time)
                    phase_modifier = SEASON_PHASES.get(season_phase, {}).get("weight", 1.0)
                    self._quantum_coherence = base_coherence * phase_modifier
                    self._current_season_phase = season_phase
                    logger.info(f" Basketball season detected - Phase: {season_phase}, Coherence: {self._quantum_coherence:.3f}")
                else:
                    self._quantum_coherence = QUANTUM_STATES["DECOHERENT"]
                    self._current_season_phase = "OFF_SEASON"
                    logger.info(f"⏹️ Off-season detected - Coherence: {self._quantum_coherence:.3f}")

            # Initialize basketball context
            self._basketball_context = {
                "season_phase": self._current_season_phase,
                "is_game_day": self._is_game_day(),
                "playoff_intensity": self._calculate_playoff_intensity(),
                "quantum_coherence": self._quantum_coherence
            }

            logger.info(" MEDUSA VAULT: Quantum state initialized with basketball awareness")

        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: initialize quantum state: {e}", exc_info=True)
            # Fallback to basic coherence
            self._quantum_coherence = QUANTUM_STATES["COHERENT"]
            self._basketball_context = {"season_phase": "UNKNOWN", "quantum_coherence": self._quantum_coherence}

    @oracle_focus
    def _initialize_expert_components(self):
        """Initialize all expert-level components with enhanced error handling."""
        # Initialize OracleMemory with quantum enhancements
        try:
            if OracleMemory is None:
                logger.warning(" OracleMemory not available, using minimal memory system")
                self.oracle_memory = None
            else:
                # Handle different config types for vault_paths
                if hasattr(self.config, 'vault_paths') and self.config.vault_paths:
                    memory_path = self.config.vault_paths.ORACLE_MEMORY
                else:
                    # Fallback for testing/validation
                    memory_path = Path("minimal_oracle_memory.db") # Use Path object

                # Handle environment settings for encryption key, pass SecretStr if OracleMemory expects it
                encryption_key_obj = None
                if self.env_settings and hasattr(self.env_settings, 'VAULT_ENCRYPTION_KEY'):
                    encryption_key_obj = self.env_settings.VAULT_ENCRYPTION_KEY
                else:
                    # Fallback for testing: create a new SecretStr
                    encryption_key_obj = SecretStr(Fernet.generate_key().decode("utf-8"))
                    logger.warning(" Using generated SecretStr for VAULT_ENCRYPTION_KEY for validation")

                memory_config = {
                    "memory_path": memory_path,
                    "encryption_key": encryption_key_obj, # Pass the SecretStr object
                    "quantum_coherence": self._quantum_coherence,
                    "basketball_context": self._basketball_context
                }
                self.oracle_memory = OracleMemory(**memory_config) # Use ** for dict unpacking

            # Log initialization with basketball context
            if self.oracle_memory is not None:
                self.oracle_memory.log_event(
                    event_type="EXPERT_ENGINE_INIT",
                    content=f"Expert Oracle Engine initialized with basketball awareness. Season: {self._current_season_phase}, Coherence: {self._quantum_coherence:.3f}",
                    tags=["initialization", "basketball", "quantum"],
                    severity=1,
                    source="ExpertOracleEngine"
                )

            logger.info(" MEDUSA VAULT: Expert Oracle Memory initialized with basketball intelligence.")

        except Exception as e:
            logger.critical(f" TITAN PROCESSING FAILED: initialize Expert Oracle Memory: {e}", exc_info=True)
            raise CosmicCollapse(f"Oracle Memory initialization failed: {e}") from e

        # Initialize Quantum Security with basketball-aware protocols
        try:
            self.quantum_security = RealQuantumSecurityEnhancements(
                memory_engine=self.oracle_memory,
                basketball_context=self._basketball_context,
                quantum_coherence=self._quantum_coherence
            )
            logger.info(" MEDUSA VAULT: Expert Quantum Security initialized with basketball protocols.")

        except Exception as e:
            logger.critical(f" TITAN PROCESSING FAILED: initialize Expert Quantum Security: {e}", exc_info=True)
            raise CosmicCollapse(f"Quantum Security initialization failed: {e}") from e

        # Initialize Temporal Flux Stabilizer with basketball rhythm synchronization
        try:
            if TemporalFluxStabilizer is None:
                logger.warning(" TemporalFluxStabilizer not available, using minimal temporal system")
                self.temporal_stabilizer = None
            else:
                # Pass the relevant config object directly to TemporalFluxStabilizer
                # This assumes TemporalFluxStabilizer's __init__ takes a config object (e.g., MockTemporalFluxConfig)
                self.temporal_stabilizer = TemporalFluxStabilizer(self.config.temporal_flux)
            logger.info(" MEDUSA VAULT: ⏳ Expert Temporal Flux Stabilizer initialized with basketball rhythm sync.")

        except Exception as e:
            logger.critical(f" TITAN PROCESSING FAILED: initialize Expert Temporal Stabilizer: {e}", exc_info=True)
            raise CosmicCollapse(f"Temporal Stabilizer initialization failed: {e}") from e

        # Initialize Quantum Messenger with basketball-aware alerting
        try:
            # Get Firebase key with fallback for testing
            firebase_key_value = "test_firebase_key_12345" # Default fallback
            if self.env_settings and hasattr(self.env_settings, 'FIREBASE_KEY') and self.env_settings.FIREBASE_KEY:
                if isinstance(self.env_settings.FIREBASE_KEY, SecretStr):
                    firebase_key_value = self.env_settings.FIREBASE_KEY.get_secret_value()
                else:
                    firebase_key_value = self.env_settings.FIREBASE_KEY # Assume it's already a string

            # Get AMBROSIA_KEY safely
            ambrosia_key_value = Fernet.generate_key().decode("utf-8") # Default fallback
            if self.env_settings and hasattr(self.env_settings, 'AMBROSIA_KEY') and self.env_settings.AMBROSIA_KEY:
                if isinstance(self.env_settings.AMBROSIA_KEY, SecretStr):
                    ambrosia_key_value = self.env_settings.AMBROSIA_KEY.get_secret_value()
                else:
                    ambrosia_key_value = self.env_settings.AMBROSIA_KEY # Assume it's already a string

            # Get PROPHECY_SIGNING_KEY safely
            prophecy_key_value = Fernet.generate_key().decode("utf-8") # Default fallback
            if self.env_settings and hasattr(self.env_settings, 'PROPHECY_SIGNING_KEY') and self.env_settings.PROPHECY_SIGNING_KEY:
                if isinstance(self.env_settings.PROPHECY_SIGNING_KEY, SecretStr):
                    prophecy_key_value = self.env_settings.PROPHECY_SIGNING_KEY.get_secret_value()
                else:
                    prophecy_key_value = self.env_settings.PROPHECY_SIGNING_KEY # Assume it's already a string

            if QuantumMessenger is None or QuantumMessengerConfig is None:
                logger.warning(" QuantumMessenger not available, using minimal messaging system")
                self.quantum_messenger = None
            else:
                messenger_config = QuantumMessengerConfig(
                    messenger_type="expert_basketball_alert",
                    max_retries=self.config.hermes_conduits.websocket.reconnect_attempts,
                    api_key=firebase_key_value,
                    base_url=self.config.hermes_conduits.websocket.url,
                    allowed_routes=["/quantum-alerts", "/temporal-log", "/basketball-alerts"],
                    quantum_key=ambrosia_key_value,
                    signature_secret=prophecy_key_value,
                    basketball_context=self._basketball_context
                )
                self.quantum_messenger = QuantumMessenger(messenger_config)
            logger.info(" MEDUSA VAULT: 📬 Expert Quantum Messenger initialized with basketball intelligence.")

            # Initialize Expert Messaging System
            if EXPERT_MESSAGING_AVAILABLE:
                try:
                    self.expert_messaging = ExpertMessagingOrchestrator()
                    logger.info(" MEDUSA VAULT: 🌟 Expert Messaging Orchestrator initialized for Oracle Engine")
                except Exception as e:
                    logger.warning(f" TITAN PROCESSING FAILED: initialize Expert Messaging Orchestrator: {e}")
                    self.expert_messaging = None
            else:
                self.expert_messaging = None

        except Exception as e:
            logger.critical(f" TITAN PROCESSING FAILED: initialize Expert Quantum Messenger: {e}", exc_info=True)
            # Don't raise, use simplified initialization instead
            logger.warning(f" Simplified initialization due to missing dependencies: Quantum Messenger initialization failed: {e}")
            logger.info(" MEDUSA VAULT: 🔧 Initializing minimal components for validation...")
            # Simplified fallback initialization continues below (handled by _initialize_minimal_components)

        # Initialize Quantum Forge with basketball-aware prediction enhancement
        try:
            if HydraGenome is None:
                logger.warning(" HydraGenome not available, using minimal forge config")
                forge_config = {
                    "basketball_intelligence": True,
                    "season_phase": self._current_season_phase,
                    "quantum_coherence": self._quantum_coherence
                }
            else:
                forge_config = HydraGenome(
                    model_registry=self.config.neural_settings.model_registry,
                    temporal_weights=self.config.neural_settings.temporal_weights,
                    ichor_dimensions=self.config.neural_settings.ichor_dimensions,
                    encryption_key=self.config.hephaestus_security.chaos_key,
                    max_retries=self.config.aegis_defense.retry_rituals,
                    temporal_flux_threshold=self.config.neural_ichor.activation_threshold,
                    basketball_intelligence=True,
                    season_phase=self._current_season_phase,
                    quantum_coherence=self._quantum_coherence
                )

            if QuantumForge is None:
                logger.warning(" QuantumForge not available, using minimal forge")
                self.quantum_forge = None
            else:
                # Check if dependencies are available
                hasher = AmbrosiaHasher() if AmbrosiaHasher is not None else None
                self.quantum_forge = QuantumForge(
                    config=forge_config,
                    security_enhancements=self.quantum_security,
                    temporal_stabilizer=self.temporal_stabilizer,
                    hasher=hasher,
                    basketball_context=self._basketball_context
                )
            logger.info(" MEDUSA VAULT: 🔨 Expert Quantum Forge initialized with basketball prediction enhancement.")

        except Exception as e:
            logger.critical(f" TITAN PROCESSING FAILED: initialize Expert Quantum Forge: {e}", exc_info=True)
            raise CosmicCollapse(f"Quantum Forge initialization failed: {e}") from e

        # Initialize IchorFlow with basketball data processing
        try:
            if IchorFlow is None:
                logger.warning(" IchorFlow not available, using minimal flow processor")
                self.ichor_flow = None
            else:
                ichor_flow_config = self._prepare_expert_ichor_config()
                self.ichor_flow = IchorFlow(ichor_flow_config)
            logger.info(" MEDUSA VAULT: 💧 Expert Ichor Flow initialized with basketball data processing.")

        except Exception as e:
            logger.critical(f" TITAN PROCESSING FAILED: initialize Expert Ichor Flow: {e}", exc_info=True)
            raise CosmicCollapse(f"Ichor Flow initialization failed: {e}") from e

    @oracle_focus
    def _initialize_basketball_intelligence(self):
        """Initialize basketball-specific intelligence systems."""
        try:
            # Setup basketball rhythm tracking
            self._basketball_rhythms = {
                "game_schedule": self._load_game_schedule(),
                "player_performance_cycles": self._initialize_player_cycles(),
                "team_chemistry_matrix": self._initialize_team_chemistry(),
                "seasonal_momentum": self._calculate_seasonal_momentum()
            }

            # Initialize playoff intensity calculator
            self._playoff_calculator = self._setup_playoff_calculator()

            # Setup clutch time protocols
            self._clutch_protocols = self._initialize_clutch_protocols()

            logger.info(" MEDUSA VAULT: Basketball Intelligence systems initialized successfully.")

        except Exception as e:
            logger.warning(f" Basketball Intelligence initialization encountered issues: {e}", exc_info=True)
            # Initialize with minimal basketball awareness
            self._basketball_rhythms = {"minimal_mode": True}
            self._playoff_calculator = lambda: 1.0
            self._clutch_protocols = {}

    @oracle_focus
    def _initialize_monitoring_systems(self):
        """Initialize advanced monitoring and performance tracking."""
        try:
            # Setup performance metrics tracking
            self._metrics_tracker = {
                "quantum_coherence_history": deque(maxlen=1000),
                "basketball_prediction_accuracy": deque(maxlen=500),
                "temporal_stability_index": deque(maxlen=200),
                "divine_intervention_log": []
            }

            # Initialize alert thresholds with basketball context
            self._alert_thresholds = {
                "low_coherence": ICHOR_VITALITY["CRITICAL_ZONE"][1],
                "high_entropy": 0.85,
                "basketball_anomaly": 0.75,
                "temporal_drift": 0.15
            }

            logger.info(" MEDUSA VAULT: ✨ Expert monitoring systems initialized.")

        except Exception as e:
            logger.error(f" TITAN PROCESSING FAILED: initialize monitoring systems: {e}", exc_info=True)
            # Fallback monitoring
            self._metrics_tracker = {}
            self._alert_thresholds = {}

    @oracle_focus
    async def eternal_vigil(self):
        """
        Initiates the eternal vigil of the Oracle Engine,
        driving continuous prophecy generation and system monitoring.
        """
        if not self._is_initialized:
            logger.critical(
                "Oracle Engine is not initialized. Cannot start eternal vigil."
            )
            raise RuntimeError("Oracle Engine not initialized.")

        logger.info(" MEDUSA VAULT: ✨ Initiating eternal vigil...")

        # Connect the messenger (if it has a connect method)
        try:
            if hasattr(self.quantum_messenger, "connect"):
                await self.quantum_messenger.connect()
                logger.info(" MEDUSA VAULT: 📬 Quantum Messenger connected.")
        except Exception as e:
            logger.warning(
                f" TITAN PROCESSING FAILED: connect Quantum Messenger: {e}", exc_info=True
            ) # Log as warning, continue if not critical

        try:
            # Main event loop for prophecy generation and monitoring
            while True:
                logger.info(" MEDUSA VAULT: Oracle Engine performing a cosmic sweep...")

                # 1. Fetch current system state / gather data for prophecy
                # This could involve calls to other modules, e.g., sensor data, market feeds
                current_state: Dict[str, Any] = self._gather_system_state() # Assuming this is a sync method

                # 2. Process data through IchorFlow pipeline
                processed_data = await self.ichor_flow.initiate_flow(current_state)

                # 3. Generate prophecy using QuantumForge
                try:
                    if self.quantum_forge is not None:
                        prophecy_report = await self.quantum_forge.generate(processed_data) # Assuming generate is async
                    else:
                        logger.warning(" QuantumForge not available, using minimal prophecy")
                        prophecy_report = {"entropy": 0.5, "confidence": 0.5, "ichor_signature": "minimal"}
                    logger.info(" MEDUSA VAULT: Prophecy generated successfully.")

                    # 4. Log the prophecy event in OracleMemory
                    if self.oracle_memory is not None:
                        self.oracle_memory.log_event(
                            event_type="PROPHECY_GENERATED",
                            content=f"Prophecy created with entropy: {prophecy_report.get('entropy'):.4f}",
                            tags=["prophecy", "generation", "success"],
                            severity=1, # Info level
                            source="QuantumForge",
                            entanglement_hash=prophecy_report.get("ichor_signature"),
                        )

                    # 5. Send alerts based on prophecy characteristics (e.g., high entropy)
                    if (
                        prophecy_report.get("entropy", 0.0)
                        > self.config.celestial_monitoring.alert_threshold
                    ):
                        alert_title = "High Prophecy Entropy Detected"
                        alert_body = f"Prophecy generated with unusually high entropy: {prophecy_report['entropy']:.4f}"
                        await self.quantum_messenger.dispatch_message( # Use dispatch_message
                            recipient="High Oracle", # Or a specific user/group
                            message={"title": alert_title, "body": alert_body},
                            message_type="critical_alert",
                        )
                        logger.warning(f"Sent alert: {alert_title}")

                except (
                    ModelIntegrityError,
                    QuantumEntanglementFailure,
                    StateValidationError,
                    RuntimeError,
                ) as e:
                    logger.error(f" Prophecy generation failed: {e}", exc_info=True)
                    if self.oracle_memory is not None:
                        self.oracle_memory.log_event(
                            event_type="PROPHECY_FAILURE",
                            content=f"Prophecy generation failed: {e}",
                            tags=["prophecy", "generation", "error"],
                            severity=4, # Error level
                            source="QuantumForge",
                        )
                    # Attempt to stabilize flux if generation fails
                    # Assuming TemporalFluxStabilizer has a way to get its current flux.
                    # This needs to be refined based on actual TemporalFluxStabilizer implementation.
                    # For now, using a placeholder method, assuming it works.
                    if self.temporal_stabilizer is not None:
                        self.temporal_stabilizer.stabilize_flux(
                            self.temporal_stabilizer.get_stability_status(
                                self.temporal_stabilizer.config.flux_threshold_critical # Example of getting a flux value
                            ) # Pass a meaningful flux value here
                        )
                    # Send alert for generation failure via expert messaging system
                    await self._send_expert_alert(
                        "Prophecy Generation Failed",
                        f"Oracle Engine TITAN PROCESSING FAILED: generate prophecy: {e}",
                        "system-errors",
                        alert_type="system_failure"
                    )

                except Exception as e:
                    logger.critical(
                        f"💥 An unexpected critical error occurred during prophecy generation cycle: {e}",
                        exc_info=True,
                    )
                    if self.oracle_memory is not None:
                        self.oracle_memory.log_event(
                            event_type="ENGINE_CRITICAL_ERROR",
                            content=f"Unexpected critical error: {e}",
                            tags=["engine", "critical", "failure"],
                            severity=5, # Critical level
                            source="OracleEngine",
                        )
                    await self._send_expert_alert(
                        "Oracle Engine Critical Failure",
                        f"A critical, unexpected error occurred: {e}",
                        "system-catastrophe",
                        alert_type="critical_failure"
                    )
                    raise CosmicCollapse(
                        "Critical failure during eternal vigil."
                    ) from e

                # 6. Perform periodic maintenance (e.g., memory rotation)
                if self.oracle_memory is not None:
                    self.oracle_memory.rotate_memory(
                        max_entries=self.config.mnemosyne_core.capacity_gb * 100
                    ) # Example scaling

                # Wait for the next cycle (using config.live_config.observables.interval for example)
                # This needs to be an awaitable sleep in an async loop
                next_cycle_interval = (
                    self.config.live_config.observables.interval
                ) # Seconds
                logger.info(
                    f"🧘‍♀️ Oracle Engine resting for {next_cycle_interval} seconds..."
                )
                await asyncio.sleep(next_cycle_interval)

        except CosmicCollapse:
            logger.critical(
                "𓀌 Cataclysmic failure during eternal vigil: Oracle Engine terminating. 𓀍"
            )
            # This exception is raised for unrecoverable errors.
            raise # Re-raise to terminate the application
        except asyncio.CancelledError:
            logger.warning(
                "Eternal vigil cancelled. Oracle Engine shutting down gracefully."
            )
        except Exception as e:
            logger.critical(
                f"𓀌 An unexpected cataclysmic failure occurred during eternal vigil: {e} 𓀍",
                exc_info=True,
            )
            # Catch any other unhandled exceptions in the loop and re-raise as CosmicCollapse
            raise CosmicCollapse(f"Eternal vigil failed: {e}") from e
        finally:
            logger.info(" MEDUSA VAULT: Eternal vigil concluded.")
            # Disconnect the messenger (if it has a disconnect method)
            if hasattr(self.quantum_messenger, "disconnect"):
                await self.quantum_messenger.disconnect()
                logger.info(" MEDUSA VAULT: 📬 Quantum Messenger disconnected.")

    @oracle_focus
    async def expert_eternal_vigil(self):
        """
        Expert-level eternal vigil with basketball-aware quantum orchestration.

        Enhanced Features:
        - Basketball rhythm synchronization with game schedule awareness
        - Quantum coherence optimization based on season phase
        - Advanced prophecy generation with basketball context integration
        - Multi-dimensional performance tracking with basketball IQ metrics
        - Intelligent temporal flux management with clutch-time acceleration
        - Divine intervention protocols with basketball context preservation
        """
        if not self._is_initialized:
            logger.critical("Expert Oracle Engine is not initialized. Cannot start eternal vigil.")
            raise CosmicCollapse("Expert Oracle Engine not initialized.")

        logger.info(" MEDUSA VAULT: ✨ Initiating Expert Eternal Vigil with Basketball Intelligence...")

        # Connect quantum messenger with basketball awareness
        try:
            if hasattr(self.quantum_messenger, "connect"):
                await self.quantum_messenger.connect()
                logger.info(" MEDUSA VAULT: 📬 Expert Quantum Messenger connected with basketball intelligence.")
        except Exception as e:
            logger.warning(f" TITAN PROCESSING FAILED: connect Expert Quantum Messenger: {e}", exc_info=True)

        try:
            vigil_cycle = 0
            while True:
                vigil_cycle += 1
                logger.info(f" Expert Oracle Engine performing cosmic sweep #{vigil_cycle}...")

                # 1. Update basketball context and quantum state
                self._update_basketball_context()
                self._update_quantum_coherence()

                # 2. Gather enhanced system state with basketball intelligence
                current_state = await self._gather_expert_system_state()

                # 3. Process data through expert ichor flow with basketball enhancements
                processed_data = await self.ichor_flow.initiate_expert_flow(
                    current_state,
                    basketball_context=self._basketball_context,
                    quantum_coherence=self._quantum_coherence
                )

                # 4. Generate expert prophecy with basketball-aware quantum enhancement
                prophecy_result = await self._generate_expert_prophecy(processed_data)

                # 5. Analyze and respond to prophecy results
                await self._process_expert_prophecy_results(prophecy_result)

                # 6. Perform expert maintenance with basketball rhythm awareness
                await self._perform_expert_maintenance()

                # 7. Update performance metrics and tracking
                self._update_expert_metrics(prophecy_result)

                # 8. Calculate next cycle interval with basketball awareness
                next_interval = self._calculate_expert_cycle_interval()
                logger.info(f"🧘‍♀️ Expert Oracle Engine resting for {next_interval:.1f} seconds (basketball-adjusted)...")
                await asyncio.sleep(next_interval)

        except CosmicCollapse:
            logger.critical("𓀌 Expert Cataclysmic failure during eternal vigil: Expert Oracle Engine terminating. 𓀍")
            raise
        except asyncio.CancelledError:
            logger.warning(" TITAN WARNING: Expert Eternal vigil cancelled. Expert Oracle Engine shutting down gracefully.")
        except Exception as e:
            logger.critical(f"𓀌 Unexpected expert cataclysmic failure during eternal vigil: {e} 𓀍", exc_info=True)
            raise CosmicCollapse(f"Expert eternal vigil failed: {e}") from e
        finally:
            logger.info(" MEDUSA VAULT: Expert Eternal vigil concluded with basketball intelligence preservation.")
            if hasattr(self.quantum_messenger, "disconnect"):
                await self.quantum_messenger.disconnect()
                logger.info(" MEDUSA VAULT: 📬 Expert Quantum Messenger disconnected.")

    # Expert Basketball-Aware Methods
    def _update_basketball_context(self):
        """Update basketball context based on current time and game data."""
        current_time = datetime.now()
        self._basketball_context.update({
            "current_time": current_time,
            "is_game_day": self._is_game_day(),
            "season_phase": self._detect_season_phase(current_time),
            "playoff_intensity": self._calculate_playoff_intensity(),
            "quantum_coherence": self._quantum_coherence
        })

    def _update_quantum_coherence(self):
        """Update quantum coherence based on basketball context."""
        base_coherence = QUANTUM_STATES["COHERENT"]
        basketball_modifier = get_basketball_context_modifier(self._basketball_context.get("season_phase", "REGULAR_SEASON"))
        season_modifier = get_quantum_state_modifier(self._quantum_coherence)

        self._quantum_coherence = min(1.0, base_coherence + basketball_modifier + (season_modifier * 0.1))
        self._temporal_flux_history.append(self._quantum_coherence)

    async def _gather_expert_system_state(self) -> Dict[str, Any]:
        """Gather comprehensive system state with basketball intelligence."""
        base_state = {
            # Assuming temporal_stabilizer has a way to get its current flux.
            # This needs to be refined based on actual TemporalFluxStabilizer implementation.
            # For now, using a placeholder method, assuming it works.
            "temporal_flux": self.temporal_stabilizer.get_stability_status(
                self.temporal_stabilizer.config.flux_threshold_critical # Example of getting a flux value
            ) if self.temporal_stabilizer is not None else 0.0,
            "quantum_coherence": self._quantum_coherence,
            "basketball_context": self._basketball_context,
            "season_phase": self._current_season_phase,
            "reality_stability": 0.95 + (self._quantum_coherence * 0.05),
            "vault_health_index": self._calculate_vault_health(),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }

        # Add basketball-specific data
        basketball_data = await self._gather_basketball_data()
        base_state["basketball_intelligence"] = basketball_data

        return base_state

    async def _gather_basketball_data(self) -> Dict[str, Any]:
        """Gather basketball-specific data for enhanced predictions."""
        return {
            "games_today": self._check_games_today(),
            "player_performance_trends": self._analyze_player_trends(),
            "team_chemistry_indices": self._calculate_team_chemistry(),
            "playoff_probabilities": self._calculate_playoff_probabilities(),
            "clutch_time_factors": self._analyze_clutch_factors()
        }

    async def _generate_expert_prophecy(self, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate expert-level prophecy with basketball-aware enhancements."""
        try:
            # Apply basketball context modifiers to forge parameters
            enhanced_data = self._apply_basketball_modifiers(processed_data)

            # Generate prophecy with quantum-basketball fusion
            if self.quantum_forge is not None:
                prophecy_report = await self.quantum_forge.generate_expert_prophecy( # Assuming generate_expert_prophecy is async
                    enhanced_data,
                    basketball_context=self._basketball_context,
                    quantum_coherence=self._quantum_coherence
                )
            else:
                logger.warning(" QuantumForge not available, using minimal expert prophecy")
                prophecy_report = {
                    "confidence": 0.5,
                    "basketball_confidence": 0.5,
                    "quantum_signature": "minimal_expert"
                }

            # Enhance prophecy with basketball intelligence
            prophecy_report = self._enhance_prophecy_with_basketball_intelligence(prophecy_report)

            logger.info(f" Expert prophecy generated with basketball intelligence - Accuracy: {prophecy_report.get('confidence', 0.0):.3f}")

            return prophecy_report

        except Exception as e:
            logger.error(f" Expert prophecy generation failed: {e}", exc_info=True)
            # Log failure with basketball context
            if self.oracle_memory is not None:
                self.oracle_memory.log_event(
                    event_type="EXPERT_PROPHECY_FAILURE",
                    content=f"Expert prophecy generation failed: {e}",
                    tags=["expert", "prophecy", "basketball", "failure"],
                    severity=4,
                    source="ExpertOracleEngine",
                    basketball_context=str(self._basketball_context)
                )
            raise

    def _apply_basketball_modifiers(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply basketball-aware modifiers to enhance prediction data."""
        enhanced_data = data.copy()

        # Apply season phase modifiers
        phase_weight = SEASON_PHASES.get(self._current_season_phase, {}).get("weight", 1.0)
        enhanced_data["season_weight"] = phase_weight

        # Apply playoff intensity
        if self._basketball_context.get("playoff_intensity", 0) > 0:
            enhanced_data["intensity_multiplier"] = self._basketball_context["playoff_intensity"]

        # Apply quantum coherence boost
        enhanced_data["quantum_boost"] = self._quantum_coherence

        return enhanced_data

    def _enhance_prophecy_with_basketball_intelligence(self, prophecy: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance prophecy results with basketball-specific intelligence."""
        enhanced_prophecy = prophecy.copy()

        # Add basketball confidence metrics
        enhanced_prophecy["basketball_confidence"] = self._calculate_basketball_confidence(prophecy)
        enhanced_prophecy["clutch_factor"] = self._calculate_clutch_factor(prophecy)
        enhanced_prophecy["momentum_index"] = self._calculate_momentum_index(prophecy)
        enhanced_prophecy["chemistry_impact"] = self._calculate_chemistry_impact(prophecy)

        return enhanced_prophecy

    async def _process_expert_prophecy_results(self, prophecy_result: Dict[str, Any]):
        """Process and respond to expert prophecy results with basketball awareness."""
        # Log prophecy with enhanced basketball context
        if self.oracle_memory is not None:
            self.oracle_memory.log_event(
                event_type="EXPERT_PROPHECY_GENERATED",
                content=f"Expert prophecy: confidence={prophecy_result.get('confidence', 0):.3f}, basketball_confidence={prophecy_result.get('basketball_confidence', 0):.3f}",
                tags=["expert", "prophecy", "basketball", "success"],
                severity=1,
                source="ExpertOracleEngine",
                basketball_context=str(self._basketball_context),
                entanglement_hash=prophecy_result.get("quantum_signature")
            )

        # Send basketball-aware alerts if needed
        await self._send_basketball_alerts(prophecy_result)

    async def _send_basketball_alerts(self, prophecy_result: Dict[str, Any]):
        """Send basketball-specific alerts based on prophecy results."""
        confidence = prophecy_result.get("confidence", 0.0)
        basketball_confidence = prophecy_result.get("basketball_confidence", 0.0)

        # High confidence basketball prediction alert
        if basketball_confidence > 0.85 and self._basketball_context.get("is_game_day", False):
            await self.quantum_messenger.dispatch_message(
                recipient="Basketball Analytics Team",
                message={
                    "title": "High-Confidence Basketball Prediction",
                    "body": f"Expert system generated high-confidence prediction (Basketball: {basketball_confidence:.3f}, Overall: {confidence:.3f})",
                    "basketball_context": self._basketball_context,
                    "season_phase": self._current_season_phase
                },
                message_type="basketball_alert"
            )

    async def _perform_expert_maintenance(self):
        """Perform expert-level maintenance with basketball rhythm awareness."""
        # Quantum coherence optimization
        self._optimize_quantum_coherence()

        # Basketball rhythm synchronization
        self._synchronize_basketball_rhythms()

        # Memory rotation with basketball context preservation
        if self.oracle_memory is not None:
            self.oracle_memory.rotate_memory_expert( # Assuming OracleMemory has this method
                max_entries=self.config.mnemosyne_core.capacity_gb * 150, # 50% larger for expert mode
                basketball_context=self._basketball_context
            )

    def _calculate_expert_cycle_interval(self) -> float:
        """Calculate next cycle interval with basketball awareness."""
        base_interval = self.config.live_config.observables.interval

        # Adjust based on basketball context
        if self._basketball_context.get("is_game_day", False):
            base_interval *= 0.5 # More frequent updates on game days

        if self._current_season_phase == "PLAYOFFS":
            base_interval *= 0.7 # More frequent during playoffs

        if self._current_season_phase == "FINALS":
            base_interval *= 0.5 # Most frequent during finals

        # Quantum coherence adjustment
        coherence_factor = 1.0 + (1.0 - self._quantum_coherence) * 0.3

        return base_interval * coherence_factor

    def _update_expert_metrics(self, prophecy_result: Dict[str, Any]):
        """Update expert performance metrics and tracking."""
        self._performance_metrics["prophecies_generated"] += 1

        if "confidence" in prophecy_result:
            confidence = prophecy_result["confidence"]
            self._metrics_tracker["quantum_coherence_history"].append(self._quantum_coherence)

        if "basketball_confidence" in prophecy_result:
            basketball_confidence = prophecy_result["basketball_confidence"]
            self._metrics_tracker["basketball_prediction_accuracy"].append(basketball_confidence)
            self._performance_metrics["basketball_context_hits"] += 1 if basketball_confidence > 0.7 else 0

    # Basketball Intelligence Helper Methods (continued)
    def _calculate_vault_health(self) -> float:
        """Calculate overall vault health with basketball context."""
        base_health = 0.88
        coherence_factor = self._quantum_coherence * 0.12
        basketball_factor = 0.05 if self._basketball_context.get("is_game_day", False) else 0.0
        return min(1.0, base_health + coherence_factor + basketball_factor)

    def _check_games_today(self) -> int:
        """Check number of games today (placeholder)."""
        return 8 if self._is_game_day() else 0

    def _analyze_player_trends(self) -> Dict[str, Any]:
        """Analyze current player performance trends."""
        return {"trending_up": 15, "trending_down": 8, "stable": 25}

    def _calculate_team_chemistry(self) -> Dict[str, Any]:
        """Calculate team chemistry indices."""
        return {"avg_chemistry": 0.75, "top_chemistry": 0.92, "teams_analyzed": 30}

    def _calculate_playoff_probabilities(self) -> Dict[str, Any]:
        """Calculate playoff probabilities for teams."""
        if self._current_season_phase in ["PLAYOFFS", "FINALS"]:
            return {"calculations_active": True, "teams_remaining": 16}
        return {"calculations_active": False}

    def _analyze_clutch_factors(self) -> Dict[str, Any]:
        """Analyze clutch time performance factors."""
        return {"clutch_leaders": 10, "clutch_threshold": 0.85}

    def _calculate_basketball_confidence(self, prophecy: Dict[str, Any]) -> float:
        """Calculate basketball-specific confidence metric."""
        base_confidence = prophecy.get("confidence", 0.5)
        basketball_boost = 0.1 if self._basketball_context.get("is_game_day", False) else 0.0
        season_boost = SEASON_PHASES.get(self._current_season_phase, {}).get("weight", 1.0) * 0.05
        return min(1.0, base_confidence + basketball_boost + season_boost)

    def _calculate_clutch_factor(self, prophecy: Dict[str, Any]) -> float:
        """Calculate clutch time factor for predictions."""
        if self._current_season_phase in ["PLAYOFFS", "FINALS"]:
            return 0.85 + (self._quantum_coherence * 0.15)
        return 0.5

    def _calculate_momentum_index(self, prophecy: Dict[str, Any]) -> float:
        """Calculate momentum index based on recent performance."""
        return self._calculate_seasonal_momentum() * self._quantum_coherence

    def _calculate_chemistry_impact(self, prophecy: Dict[str, Any]) -> float:
        """Calculate team chemistry impact on predictions."""
        base_chemistry = 0.75
        coherence_boost = self._quantum_coherence * 0.2
        return min(1.0, base_chemistry + coherence_boost)

    def _optimize_quantum_coherence(self):
        """Optimize quantum coherence based on basketball performance."""
        if len(self._temporal_flux_history) > 10:
            avg_coherence = statistics.mean(list(self._temporal_flux_history)[-10:])
            if avg_coherence < ICHOR_VITALITY["CRITICAL_ZONE"][1]:
                self._quantum_coherence = min(1.0, self._quantum_coherence * 1.1)
                logger.info(f"🔧 Quantum coherence optimized: {self._quantum_coherence:.3f}")

    def _synchronize_basketball_rhythms(self):
        """Synchronize system with basketball rhythms and schedules."""
        current_time = datetime.now()
        game_day_factor = 1.2 if self._is_game_day() else 1.0
        # Update basketball rhythms
        self._basketball_rhythms["synchronization_factor"] = game_day_factor
        self._basketball_rhythms["last_sync"] = current_time.isoformat()


    # Core Basketball Intelligence Methods
    def _is_basketball_season(self, current_time: datetime) -> bool:
        """Determine if current time is within basketball season."""
        # NBA season typically runs from October to June
        month = current_time.month
        return month >= 10 or month <= 6

    def _detect_season_phase(self, current_time: datetime) -> str:
        """Detect current season phase based on date."""
        month = current_time.month
        day = current_time.day

        if month == 9 or (month == 10 and day < 15):
            return "PRESEASON"
        elif month == 10 or month == 11:
            return "EARLY_SEASON"
        elif month == 12 or month == 1 or month == 2:
            return "MID_SEASON"
        elif month == 3:
            return "LATE_SEASON"
        elif month == 4 or month == 5:
            return "PLAYOFFS"
        elif month == 6 and day < 25:
            return "FINALS"
        else:
            return "OFFSEASON"

    def _is_game_day(self) -> bool:
        """Check if today is a game day (simplified logic)."""
        # Simplified logic - could be enhanced with real schedule data
        current_time = datetime.now()
        return current_time.weekday() in [1, 2, 4, 5, 6] # Tue, Wed, Fri, Sat, Sun

    def _calculate_playoff_intensity(self) -> float:
        """Calculate playoff intensity multiplier."""
        if self._current_season_phase == "PLAYOFFS":
            return BASKETBALL_CONTEXT.get("PLAYOFF_INTENSITY", 0.12)
        elif self._current_season_phase == "FINALS":
            return BASKETBALL_CONTEXT.get("PLAYOFF_INTENSITY", 0.12) * 1.5
        return 0.0

    def _load_game_schedule(self) -> Dict[str, Any]:
        """Load game schedule (placeholder implementation)."""
        return {"games_loaded": True, "total_games": 82}

    def _initialize_player_cycles(self) -> Dict[str, Any]:
        """Initialize player performance cycles tracking."""
        return {"players_tracked": 450, "cycles_active": True}

    def _initialize_team_chemistry(self) -> Dict[str, Any]:
        """Initialize team chemistry matrix."""
        return {"teams": 30, "chemistry_matrix": "initialized"}

    def _calculate_seasonal_momentum(self) -> float:
        """Calculate seasonal momentum factor."""
        phase_weights = SEASON_PHASES.get(self._current_season_phase, {})
        return phase_weights.get("weight", 1.0) * self._quantum_coherence

    def _setup_playoff_calculator(self):
        """Setup playoff probability calculator."""
        def calculator():
            if self._current_season_phase in ["PLAYOFFS", "FINALS"]:
                return 1.0 + self._quantum_coherence
            return 1.0
        return calculator

    def _initialize_clutch_protocols(self) -> Dict[str, Any]:
        """Initialize clutch time protocols."""
        return {
            "clutch_threshold": 0.85,
            "acceleration_factor": 1.5,
            "protocols_active": True
        }

    def _prepare_expert_ichor_config(self):
        """Prepare expert-level ichor flow configuration."""
        # _IchorFlow_ActualIchorConfig is aliased from vault_oracle.essence.ichor_flow
        # This block assumes it is a Pydantic BaseModel and requires specific fields.
        try:
            # Ensure all required fields for _IchorFlow_ActualIchorConfig are provided
            # from self.config or self.env_settings
            ichor_config_instance = _IchorFlow_ActualIchorConfig(
                activation_threshold=self.config.neural_ichor.activation_threshold,
                log_path=(
                    self.config.vault_paths.ICHOR_LOGS
                    if hasattr(self.config.vault_paths, "ICHOR_LOGS")
                    else Path("./default_ichor_log.log")
                ),
                encryption_key=self.env_settings.VAULT_ENCRYPTION_KEY.get_secret_value(), # Assumes SecretStr needs value
                basketball_context=self._basketball_context,
                quantum_coherence=self._quantum_coherence
            )
            return ichor_config_instance
        except Exception as e:
            logger.warning(f" TITAN PROCESSING FAILED: create expert ichor config: {e}. Falling back to neural_ichor config.", exc_info=True)
            # If instantiation fails, return the existing config object
            return self.config.neural_ichor

    def _initialize_minimal_components(self):
        """Initialize minimal components for testing/validation scenarios."""
        logger.info(" MEDUSA VAULT: 🔧 Initializing minimal components for validation...")

        # Create a simple quantum entangler if available
        try:
            self.quantum_entangler = QuantumEntangler()
            logger.info(" MEDUSA VAULT: 🔗 Quantum entangler initialized")
        except Exception as e:
            logger.warning(f" Could not initialize quantum entangler: {e}")
            self.quantum_entangler = None

        # Initialize minimal memory if not already available
        if not hasattr(self, 'oracle_memory') or self.oracle_memory is None:
            try:
                # Use ExpertOracleMemory if it's explicitly available and preferred for minimal setup
                # Otherwise, fall back to OracleMemory
                if 'ExpertOracleMemory' in globals() and isinstance(globals()['ExpertOracleMemory'], type):
                    self.oracle_memory = ExpertOracleMemory(
                        memory_path=Path("minimal_expert_oracle_memory.db"), # Using Path
                        enable_basketball_analytics=True,
                        encryption_key=SecretStr(Fernet.generate_key().decode("utf-8")), # Provide a key for minimal memory
                        quantum_coherence=self._quantum_coherence,
                        basketball_context=self._basketball_context
                    )
                else:
                    self.oracle_memory = OracleMemory(
                        memory_path=Path("minimal_oracle_memory.db"), # Using Path
                        encryption_key=SecretStr(Fernet.generate_key().decode("utf-8")),
                        quantum_coherence=self._quantum_coherence,
                        basketball_context=self._basketball_context
                    )
                logger.info(" MEDUSA VAULT: Minimal oracle memory initialized")
            except Exception as e:
                logger.warning(f" Could not initialize minimal memory: {e}", exc_info=True)
                self.oracle_memory = None

        logger.info(" MEDUSA VAULT: Minimal components initialization complete")

    async def _send_expert_alert(self, title: str, message: str, topic: str, alert_type: str = "system", priority: str = "normal"):
        """Send alert via expert messaging system with fallback to legacy messaging."""
        try:
            if self.expert_messaging:
                await self.expert_messaging.send_alert(
                    title=title,
                    message=message,
                    topic=topic,
                    alert_type=alert_type,
                    priority=priority,
                    context={"source": "OracleEngine", "basketball_context": self._basketball_context}
                )
                return True
        except Exception as e:
            logger.warning(f"Expert messaging failed, falling back to legacy: {e}", exc_info=True)

        # Fallback to legacy quantum messenger (using dispatch_message for consistency)
        try:
            if hasattr(self.quantum_messenger, "dispatch_message"):
                message_dict = {
                    "title": title,
                    "body": message,
                    "topic": topic, # Include topic in message body if dispatch_message uses it
                    "alert_type": alert_type,
                    "priority": priority,
                    "context": {"source": "OracleEngine_Legacy", "basketball_context": self._basketball_context}
                }
                await self.quantum_messenger.dispatch_message(
                    recipient="Legacy Recipient", # Or a generic default
                    message=message_dict,
                    message_type=alert_type # Use alert_type as message_type
                )
                return True
        except Exception as e:
            logger.error(f"Legacy messaging also failed: {e}", exc_info=True)

        return False

    # Define _gather_system_state as it's used in eternal_vigil but not defined
    @oracle_focus
    def _gather_system_state(self) -> Dict[str, Any]:
        """Placeholder for gathering basic system state."""
        return {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "status": "operational",
            "coherence": self._quantum_coherence,
            "flux": self.temporal_stabilizer.get_stability_status(self.temporal_stabilizer.config.flux_threshold_warning) if self.temporal_stabilizer is not None else 0.0 # Example
        }


# Maintain backward compatibility
OracleEngine = ExpertOracleEngine

# For backwards compatibility with validation scripts - use aliases instead of inheritance
OracleEngineState = OracleState
BasketballSeasonPhase = BasketballSeason

# --- Main Application Entry Point ---
async def main_runner(config_path: Optional[str] = None):
    """
    The main asynchronous runner function for the Expert Oracle Engine.
    Handles configuration loading and expert engine startup with basketball intelligence.

    Args:
        config_path (Optional[str]): The path to the TOML configuration file.
        If None, defaults to environment-specific config.
    """
    logger.info(" MEDUSA VAULT: --- Starting Expert Oracle Engine with Basketball Intelligence ---")
    logger.info(" MEDUSA VAULT: Expert Oracle Engine startup initiated.")

    config = None
    env_settings = None

    try:
        # Step 1: Get environment from environment variables using ConfigValidator
        temp_env_validator = ConfigValidator()
        env_from_settings = (
            temp_env_validator.system_config.environment
            if hasattr(temp_env_validator, "system_config")
            and hasattr(temp_env_validator.system_config, "environment")
            else "development"
        )

        # Step 2: Load configuration with VaultLoader
        config = VaultLoader.load_config(env=env_from_settings, config_file=config_path)
        logger.info(f"✨ Initiating expert eternal vigil with config: {config_path or 'environment-default'}")

        # Step 3: Retrieve environment settings from VaultLoader
        env_settings = VaultLoader.get_env_settings()

        # Initialize the Expert Oracle Engine with basketball intelligence
        engine = ExpertOracleEngine(config, env_settings)
        logger.info(" MEDUSA VAULT: Expert Oracle Engine initialized successfully with basketball intelligence.")

        # Start the expert eternal vigil with basketball awareness
        await engine.expert_eternal_vigil() # Corrected: Call the expert vigil

    except FileNotFoundError as e:
        logger.critical(f"𓀌 Expert Cosmic Collapse: Configuration file not found: {e} 𓀍")
        sys.exit(1)
    except ValueError as e:
        logger.critical(f"𓀌 Expert Cosmic Collapse: Invalid configuration: {e} 𓀍")
        sys.exit(1)
    except RuntimeError as e:
        logger.critical(f"𓀌 Expert Cataclysmic failure during eternal vigil: {e} 𓀍")
        sys.exit(1)
    except CosmicCollapse as e:
        logger.critical(f"𓀌 Expert Cataclysmic failure during eternal vigil: {e} 𓀍")
        sys.exit(1)
    except Exception as e:
        logger.critical(f"𓀌 Expert unexpected cataclysmic failure occurred: {e} 𓀍", exc_info=True)
        sys.exit(1)
    finally:
        logger.info(" MEDUSA VAULT: Expert Oracle Engine process concluded with basketball intelligence preservation.")


if __name__ == "__main__":
    # Expert Oracle Engine entry point with basketball intelligence
    # Expects one command-line argument: the path to the TOML configuration file

    # --- Enhanced cleanup for expert mode ---
    # Ensure the 'prod_data' directory exists for memory vault files
    Path("./prod_data").mkdir(parents=True, exist_ok=True)

    memory_db_path = Path("./prod_data/oracle_memory.db")
    memory_db_backup_path = Path("./prod_data/oracle_memory.db.bkp")
    expert_memory_path = Path("./prod_data/expert_oracle_memory.db")
    minimal_oracle_memory_path = Path("./minimal_oracle_memory.db") # Added for minimal setup
    minimal_expert_oracle_memory_path = Path("./minimal_expert_oracle_memory.db") # Added for minimal setup


    for db_path in [memory_db_path, memory_db_backup_path, expert_memory_path,
                   minimal_oracle_memory_path, minimal_expert_oracle_memory_path]:
        if db_path.exists():
            try:
                os.remove(db_path)
                logger.info(f"🧹 Cleaned up existing memory file: {db_path}")
            except OSError as e:
                logger.warning(f" Could not remove existing memory file {db_path}: {e}")
    # --- End Enhanced Cleanup ---

    if len(sys.argv) < 2:
        logger.critical("Usage: python oracle_engine.py <path_to_config_toml>")
        sys.exit(1)

    config_file_path = sys.argv[1]
    logger.info(f" Starting Expert Oracle Engine with basketball intelligence using config: {config_file_path}")
    asyncio.run(main_runner(config_file_path))
